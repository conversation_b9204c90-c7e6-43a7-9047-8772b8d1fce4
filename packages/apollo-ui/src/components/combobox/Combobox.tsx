import { HTMLProps, useCallback, useMemo, useRef } from "react"
import { Combobox as BaseCombobox } from "@base-ui-components/react/combobox"
import classNames from "classnames"

import { Chip } from "../chip"
import { ChevronIcon, CloseIcon } from "../common"
import { Input, type InputProps } from "../input"
import { MenuItem } from "../menu-item"
import { Portal } from "../portal"
import styles from "./combobox.module.css"
import type {
  ComboboxOption,
  ComboboxProps,
  ComboboxValueType,
} from "./ComboboxProps"

export function Combobox<
  ItemValue extends ComboboxOption,
  Multiple extends boolean | undefined = false,
>({
  label,
  fullWidth,
  required,
  helperText,
  error,
  disabled,
  placeholder,
  options,
  ref,
  onValueChange,
  labelDecorator,
  helperTextDecorator,
  size = "medium",
  slotProps,
  multiple,
  ...baseRootProps
}: ComboboxProps<ItemValue, Multiple>) {
  const internalRef = useRef<HTMLDivElement | null>(null)
  const containerRef = useMemo(() => ref ?? internalRef, [ref])

  const handleValueChange = useCallback(
    (value: Multiple extends true ? ItemValue[] : ItemValue, _eventDetails: any) => {
      onValueChange?.(value as ComboboxValueType<ItemValue, Multiple> | null)
    },
    [onValueChange]
  )


  return (
    <BaseCombobox.Root
      {...baseRootProps}
      multiple={multiple}
      items={options}
      itemToStringLabel={(selectedValue) => options.find((option) => option.id === selectedValue)?.label ?? ""}
      onValueChange={handleValueChange}
    >
      <div
        className={classNames(
          "ApolloCombobox-container",
          styles.comboboxRoot,
          { [styles.comboboxRootFullWidth]: fullWidth },
          slotProps?.container?.className
        )}
        ref={containerRef}
      >
        <BaseCombobox.Input
          render={(
            inputProps: HTMLProps<HTMLInputElement> & InputProps,
            inputState: BaseCombobox.Input.State
          ) => (
            <BaseCombobox.Value>
              {(currentValue: ItemValue[]) => {
                console.log("currentValue", currentValue)

                if (slotProps?.input?.render) {
                  return slotProps.input.render(inputProps, inputState)
                }
                return (
                  <Input
                    className={classNames(
                      "ApolloCombobox-input",
                      inputProps?.className,
                      {
                        [styles.comboboxInputWithChips]: multiple,
                      },
                      slotProps?.input?.className
                    )}
                    size={size}
                    label={label}
                    required={required}
                    helperText={helperText}
                    error={error}
                    fullWidth={fullWidth}
                    disabled={disabled || inputState.disabled}
                    labelDecorator={labelDecorator}
                    helperTextDecorator={helperTextDecorator}
                    placeholder={placeholder}
                    startDecorator={
                      multiple &&
                      Array.isArray(currentValue) &&
                      currentValue.length > 0 && (
                        <BaseCombobox.Chips
                          className={classNames(
                            "ApolloCombobox-chips",
                            styles.comboboxChips,
                            slotProps?.chips?.className
                          )}
                        >
                          {currentValue.map((option) => (
                            <BaseCombobox.Chip
                              key={"chip-" + option.id}
                              render={(
                                props: HTMLProps<HTMLDivElement>,
                                state: BaseCombobox.Chip.State
                              ) => {
                                if (slotProps?.chip?.render) {
                                  return slotProps.chip.render(
                                    props,
                                    state
                                  ) as React.ReactElement
                                }
                                return (
                                  <Chip
                                    label={option.label}
                                    size={size === "small" ? "small" : "medium"}
                                    disabled={state.disabled || disabled}
                                    onClose={(event) => {
                                      event.stopPropagation()
                                      // Filter out the removed option
                                      const remainingOptions =
                                        currentValue.filter(
                                          (opt) => opt.id !== option.id
                                        )
                                      onValueChange?.(
                                        remainingOptions as ComboboxValueType<
                                          ItemValue,
                                          Multiple
                                        >
                                      )
                                    }}
                                    className={classNames(
                                      "ApolloCombobox-chip",
                                      slotProps?.chip?.className
                                    )}
                                  />
                                )
                              }}
                            />
                          ))}
                        </BaseCombobox.Chips>
                      )
                    }
                    endDecorator={
                      <div
                        className={classNames(
                          "ApolloCombobox-endDecorator",
                          styles.comboboxInputEndDecorator
                        )}
                      >
                        {currentValue && (
                          <BaseCombobox.Clear
                            className={styles.comboboxClear}
                            aria-label="Clear selection"
                            disabled={disabled}
                          >
                            <CloseIcon />
                          </BaseCombobox.Clear>
                        )}
                        <BaseCombobox.Trigger
                          className={styles.comboboxTrigger}
                          aria-label="Open popup"
                          disabled={disabled}
                        >
                          <ChevronIcon />
                        </BaseCombobox.Trigger>
                      </div>
                    }
                    {...inputProps}
                  />
                )
              }}
            </BaseCombobox.Value>
          )}
        />
      </div>
      <Portal baseComponent={<BaseCombobox.Portal />}>
        <BaseCombobox.Positioner
          positionMethod="fixed"
          sticky
          sideOffset={4}
          anchor={containerRef}
          className={classNames(
            "ApolloCombobox-positioner",
            styles.comboboxMenuPositioner,
            slotProps?.positioner?.className
          )}
        >
          <BaseCombobox.Popup
            className={classNames(
              "ApolloCombobox-popup",
              styles.Popup,
              slotProps?.popup?.className
            )}
          >
            <BaseCombobox.List>
              {(item: ItemValue) => {
                return (
                  <BaseCombobox.Item
                    key={"item-" + item.id}
                    value={item.id}
                    disabled={item?.disabled}
                    className={classNames(
                      "ApolloCombobox-option",
                      styles.comboboxMenuItem
                    )}
                    render={(
                      props: HTMLProps<HTMLDivElement>,
                      state: BaseCombobox.Item.State
                    ) => {
                      if (slotProps?.option?.render) {
                        return slotProps.option.render(
                          props,
                          state
                        ) as React.ReactElement
                      }
                      if (item.renderLabel) {
                        return item.renderLabel() as React.ReactElement
                      }
                      return (
                        <MenuItem
                          label={item.label}
                          {...props}
                          className={classNames(
                            "ApolloCombobox-menuItem",
                            props?.className
                          )}
                          selected={state.selected}
                          disabled={state.disabled}
                        />
                      )
                    }}
                  />
                )
              }}
            </BaseCombobox.List>
          </BaseCombobox.Popup>
        </BaseCombobox.Positioner>
      </Portal>
    </BaseCombobox.Root>
  )
}
